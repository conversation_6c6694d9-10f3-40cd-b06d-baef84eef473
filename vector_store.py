"""
向量数据库和检索模块
使用ChromaDB存储文档向量，实现相似性检索
"""
import uuid
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from loguru import logger
from config import config
import jieba
import re

@dataclass
class Document:
    """文档数据结构"""
    id: str
    content: str
    metadata: Dict
    embedding: Optional[List[float]] = None

@dataclass
class SearchResult:
    """搜索结果"""
    document: Document
    score: float

class TextSplitter:
    """文本分割器"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
    
    def split_text(self, text: str) -> List[str]:
        """分割文本为chunks"""
        if not text:
            return []
        
        # 按段落分割
        paragraphs = re.split(r'\n\s*\n', text)
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # 如果当前chunk加上新段落超过限制
            if len(current_chunk) + len(paragraph) > self.chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    # 保留重叠部分
                    if len(paragraph) > self.chunk_overlap:
                        current_chunk = paragraph[-self.chunk_overlap:]
                    else:
                        current_chunk = paragraph
                else:
                    # 段落本身就很长，需要进一步分割
                    sentences = re.split(r'[。！？]', paragraph)
                    temp_chunk = ""
                    for sentence in sentences:
                        if len(temp_chunk) + len(sentence) > self.chunk_size:
                            if temp_chunk:
                                chunks.append(temp_chunk.strip())
                            temp_chunk = sentence
                        else:
                            temp_chunk += sentence + "。"
                    current_chunk = temp_chunk
            else:
                current_chunk += "\n" + paragraph if current_chunk else paragraph
        
        # 添加最后一个chunk
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return [chunk for chunk in chunks if len(chunk) > 50]  # 过滤太短的chunk

class VectorStore:
    """向量数据库"""
    
    def __init__(self):
        self.embedding_model = SentenceTransformer(config.EMBEDDING_MODEL)
        self.text_splitter = TextSplitter(
            chunk_size=config.MAX_CHUNK_SIZE,
            chunk_overlap=config.CHUNK_OVERLAP
        )
        
        # 初始化ChromaDB
        self.client = chromadb.PersistentClient(
            path=str(config.VECTOR_DB_DIR),
            settings=Settings(anonymized_telemetry=False)
        )
        
        # 获取或创建集合
        try:
            self.collection = self.client.get_collection(config.VECTOR_DB_COLLECTION)
            logger.info(f"加载现有集合: {config.VECTOR_DB_COLLECTION}")
        except:
            self.collection = self.client.create_collection(
                name=config.VECTOR_DB_COLLECTION,
                metadata={"description": "研报文档向量存储"}
            )
            logger.info(f"创建新集合: {config.VECTOR_DB_COLLECTION}")
    
    def generate_embedding(self, text: str) -> List[float]:
        """生成文本嵌入向量"""
        try:
            embedding = self.embedding_model.encode(text, normalize_embeddings=True)
            return embedding.tolist()
        except Exception as e:
            logger.error(f"生成嵌入向量失败: {e}")
            return []
    
    def add_document(self, content: str, metadata: Dict) -> List[str]:
        """添加文档到向量数据库"""
        try:
            # 分割文本
            chunks = self.text_splitter.split_text(content)
            logger.info(f"文档分割为 {len(chunks)} 个chunks")
            
            if not chunks:
                logger.warning("文档分割后为空")
                return []
            
            # 为每个chunk生成ID和嵌入向量
            doc_ids = []
            embeddings = []
            chunk_contents = []
            chunk_metadatas = []
            
            for i, chunk in enumerate(chunks):
                doc_id = str(uuid.uuid4())
                doc_ids.append(doc_id)
                
                # 生成嵌入向量
                embedding = self.generate_embedding(chunk)
                if not embedding:
                    logger.warning(f"跳过chunk {i}: 嵌入向量生成失败")
                    continue
                
                embeddings.append(embedding)
                chunk_contents.append(chunk)
                
                # 添加chunk元数据
                chunk_metadata = metadata.copy()
                chunk_metadata.update({
                    'chunk_id': i,
                    'chunk_length': len(chunk)
                })
                chunk_metadatas.append(chunk_metadata)
            
            # 批量添加到ChromaDB
            if embeddings:
                self.collection.add(
                    ids=doc_ids[:len(embeddings)],
                    embeddings=embeddings,
                    documents=chunk_contents,
                    metadatas=chunk_metadatas
                )
                logger.info(f"成功添加 {len(embeddings)} 个文档chunks")
                return doc_ids[:len(embeddings)]
            else:
                logger.warning("没有有效的嵌入向量")
                return []
                
        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            return []
    
    def search(self, query: str, top_k: int = None, filter_metadata: Dict = None) -> List[SearchResult]:
        """搜索相似文档"""
        try:
            if top_k is None:
                top_k = config.TOP_K_RETRIEVAL
            
            # 生成查询向量
            query_embedding = self.generate_embedding(query)
            if not query_embedding:
                logger.error("查询向量生成失败")
                return []
            
            # 执行搜索
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                where=filter_metadata
            )
            
            # 解析结果
            search_results = []
            if results['documents'] and results['documents'][0]:
                for i in range(len(results['documents'][0])):
                    doc = Document(
                        id=results['ids'][0][i],
                        content=results['documents'][0][i],
                        metadata=results['metadatas'][0][i]
                    )
                    score = 1 - results['distances'][0][i]  # 转换为相似度分数
                    
                    # 过滤低相似度结果
                    if score >= config.SIMILARITY_THRESHOLD:
                        search_results.append(SearchResult(document=doc, score=score))
            
            logger.info(f"搜索完成，返回 {len(search_results)} 个结果")
            return search_results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    def get_collection_stats(self) -> Dict:
        """获取集合统计信息"""
        try:
            count = self.collection.count()
            return {
                'total_documents': count,
                'collection_name': config.VECTOR_DB_COLLECTION
            }
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def delete_documents(self, doc_ids: List[str]) -> bool:
        """删除文档"""
        try:
            self.collection.delete(ids=doc_ids)
            logger.info(f"成功删除 {len(doc_ids)} 个文档")
            return True
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return False
    
    def clear_collection(self) -> bool:
        """清空集合"""
        try:
            # 删除现有集合
            self.client.delete_collection(config.VECTOR_DB_COLLECTION)
            # 重新创建集合
            self.collection = self.client.create_collection(
                name=config.VECTOR_DB_COLLECTION,
                metadata={"description": "研报文档向量存储"}
            )
            logger.info("集合已清空")
            return True
        except Exception as e:
            logger.error(f"清空集合失败: {e}")
            return False

def main():
    """测试函数"""
    vector_store = VectorStore()
    
    # 测试添加文档
    test_content = """
    太辰光是一家专业从事光通信器件研发、生产和销售的高新技术企业。
    公司主要产品包括光纤连接器、光纤适配器、光纤跳线等。
    随着5G和数据中心建设的推进，公司业务快速增长。
    2024年第三季度，公司营收同比增长25%，净利润同比增长30%。
    """
    
    metadata = {
        'filename': 'test_report.pdf',
        'company': '太辰光',
        'stock_code': '300570',
        'date': '20241030',
        'institution': '测试机构'
    }
    
    doc_ids = vector_store.add_document(test_content, metadata)
    print(f"添加文档，生成ID: {doc_ids}")
    
    # 测试搜索
    query = "太辰光业绩增长情况"
    results = vector_store.search(query, top_k=3)
    
    print(f"\n搜索结果 (查询: {query}):")
    for result in results:
        print(f"相似度: {result.score:.3f}")
        print(f"内容: {result.document.content[:100]}...")
        print(f"元数据: {result.document.metadata}")
        print("-" * 50)
    
    # 获取统计信息
    stats = vector_store.get_collection_stats()
    print(f"\n集合统计: {stats}")

if __name__ == "__main__":
    main()
