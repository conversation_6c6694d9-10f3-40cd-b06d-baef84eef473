"""
RAG问答系统
基于检索增强生成，结合召回的研报知识回答用户问题
"""
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from openai import OpenAI
from loguru import logger
from vector_store import VectorStore, SearchResult
from config import config
import json

@dataclass
class RAGResponse:
    """RAG响应结果"""
    answer: str
    sources: List[Dict]
    confidence: float
    reasoning: str

class RAGSystem:
    """RAG问答系统"""
    
    def __init__(self):
        self.client = OpenAI(
            api_key=config.OPENAI_API_KEY,
            base_url=config.OPENAI_BASE_URL
        )
        self.model = config.OPENAI_MODEL
        self.vector_store = VectorStore()
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是一个专业的金融分析师和投资顾问，专门分析股票研报并回答投资相关问题。

你的任务是：
1. 基于提供的研报内容，准确回答用户的问题
2. 提供客观、专业的分析和建议
3. 引用具体的数据和事实支撑你的观点
4. 如果信息不足，明确说明并建议需要更多信息
5. 保持中性立场，不做过度乐观或悲观的判断

回答要求：
- 结构清晰，逻辑严谨
- 引用具体的研报内容和数据
- 提供投资建议时要谨慎，强调风险
- 使用专业但易懂的语言
"""
    
    def get_rag_prompt(self, query: str, context: str) -> str:
        """获取RAG提示词"""
        return f"""
基于以下研报内容，回答用户的问题。请确保答案准确、客观，并引用相关的研报内容。

研报内容：
{context}

用户问题：{query}

请提供详细的分析和回答，包括：
1. 直接回答问题
2. 支撑数据和证据
3. 相关的投资逻辑
4. 可能的风险因素
5. 投资建议（如适用）

如果研报内容不足以回答问题，请明确说明并建议需要哪些额外信息。
"""
    
    def retrieve_relevant_documents(self, query: str, top_k: int = None) -> List[SearchResult]:
        """检索相关文档"""
        if top_k is None:
            top_k = config.TOP_K_RETRIEVAL
        
        logger.info(f"检索相关文档: {query}")
        results = self.vector_store.search(query, top_k=top_k)
        
        logger.info(f"检索到 {len(results)} 个相关文档")
        return results
    
    def prepare_context(self, search_results: List[SearchResult]) -> Tuple[str, List[Dict]]:
        """准备上下文和来源信息"""
        context_parts = []
        sources = []
        
        for i, result in enumerate(search_results):
            # 添加到上下文
            context_parts.append(f"[文档{i+1}] {result.document.content}")
            
            # 记录来源
            source = {
                'index': i + 1,
                'content': result.document.content[:200] + "..." if len(result.document.content) > 200 else result.document.content,
                'metadata': result.document.metadata,
                'similarity_score': result.score
            }
            sources.append(source)
        
        context = "\n\n".join(context_parts)
        return context, sources
    
    def generate_answer(self, query: str, context: str) -> Tuple[str, str]:
        """生成答案"""
        try:
            prompt = self.get_rag_prompt(query, context)
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=1500
            )
            
            answer = response.choices[0].message.content
            
            # 生成推理过程
            reasoning_prompt = f"""
基于以下问答过程，简要说明你的推理逻辑：

问题：{query}
答案：{answer}

请用1-2句话说明你是如何得出这个答案的，主要依据了哪些关键信息。
"""
            
            reasoning_response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": reasoning_prompt}
                ],
                temperature=0.1,
                max_tokens=200
            )
            
            reasoning = reasoning_response.choices[0].message.content
            
            return answer, reasoning
            
        except Exception as e:
            logger.error(f"生成答案失败: {e}")
            return "抱歉，生成答案时出现错误。", "系统错误"
    
    def calculate_confidence(self, search_results: List[SearchResult], answer: str) -> float:
        """计算答案置信度"""
        if not search_results:
            return 0.0
        
        # 基于检索结果的相似度计算置信度
        avg_similarity = sum(result.score for result in search_results) / len(search_results)
        
        # 基于答案长度和内容质量调整
        answer_quality = min(len(answer) / 500, 1.0)  # 答案长度因子
        
        # 基于检索结果数量调整
        result_count_factor = min(len(search_results) / config.TOP_K_RETRIEVAL, 1.0)
        
        confidence = (avg_similarity * 0.6 + answer_quality * 0.2 + result_count_factor * 0.2)
        return round(confidence, 2)
    
    def answer_question(self, query: str, top_k: int = None) -> RAGResponse:
        """回答问题的主函数"""
        logger.info(f"开始处理问题: {query}")
        
        # 1. 检索相关文档
        search_results = self.retrieve_relevant_documents(query, top_k)
        
        if not search_results:
            return RAGResponse(
                answer="抱歉，没有找到相关的研报内容来回答您的问题。请尝试使用不同的关键词或确保已上传相关研报。",
                sources=[],
                confidence=0.0,
                reasoning="未找到相关文档"
            )
        
        # 2. 准备上下文
        context, sources = self.prepare_context(search_results)
        
        # 3. 生成答案
        answer, reasoning = self.generate_answer(query, context)
        
        # 4. 计算置信度
        confidence = self.calculate_confidence(search_results, answer)
        
        logger.info(f"问题处理完成，置信度: {confidence}")
        
        return RAGResponse(
            answer=answer,
            sources=sources,
            confidence=confidence,
            reasoning=reasoning
        )
    
    def batch_answer_questions(self, questions: List[str]) -> List[RAGResponse]:
        """批量回答问题"""
        responses = []
        for question in questions:
            response = self.answer_question(question)
            responses.append(response)
        return responses
    
    def get_related_questions(self, query: str) -> List[str]:
        """生成相关问题建议"""
        try:
            prompt = f"""
基于用户的问题："{query}"

请生成3-5个相关的问题，这些问题可能是用户接下来想了解的。
问题应该围绕投资分析、财务数据、行业趋势、风险评估等方面。

请以JSON列表格式返回：
["问题1", "问题2", "问题3"]
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=300
            )
            
            result = response.choices[0].message.content
            
            # 尝试解析JSON
            try:
                import re
                json_match = re.search(r'\[.*\]', result, re.DOTALL)
                if json_match:
                    questions = json.loads(json_match.group())
                    return questions[:5]  # 最多返回5个问题
            except:
                pass
            
            return []
            
        except Exception as e:
            logger.error(f"生成相关问题失败: {e}")
            return []

def main():
    """测试函数"""
    rag_system = RAGSystem()
    
    # 测试问题
    test_questions = [
        "太辰光的主要业务是什么？",
        "公司最近的财务表现如何？",
        "AI对太辰光业务的影响是什么？",
        "公司面临的主要风险有哪些？",
        "分析师对太辰光的投资建议是什么？"
    ]
    
    for question in test_questions:
        print(f"\n问题: {question}")
        print("=" * 50)
        
        response = rag_system.answer_question(question)
        
        print(f"答案: {response.answer}")
        print(f"置信度: {response.confidence}")
        print(f"推理: {response.reasoning}")
        print(f"来源数量: {len(response.sources)}")
        
        if response.sources:
            print("\n主要来源:")
            for source in response.sources[:2]:  # 只显示前2个来源
                print(f"- 文档{source['index']}: {source['content'][:100]}...")
        
        # 获取相关问题
        related = rag_system.get_related_questions(question)
        if related:
            print(f"\n相关问题建议: {related}")

if __name__ == "__main__":
    main()
