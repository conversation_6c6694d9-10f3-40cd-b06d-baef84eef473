"""
Streamlit Web界面
提供研报上传、查询、结果展示等功能
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from pathlib import Path
import json
import tempfile
import os
from typing import Dict, List

from main_processor import MainProcessor
from rag_system import RAGSystem
from config import config

# 页面配置
st.set_page_config(
    page_title="研报RAG分析系统",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 初始化会话状态
if 'processor' not in st.session_state:
    st.session_state.processor = MainProcessor()
if 'rag_system' not in st.session_state:
    st.session_state.rag_system = RAGSystem()
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []

def main():
    """主界面"""
    st.title("📊 研报RAG分析系统")
    st.markdown("---")
    
    # 侧边栏
    with st.sidebar:
        st.header("系统功能")
        page = st.selectbox(
            "选择功能页面",
            ["系统概览", "文档管理", "智能问答", "数据分析", "系统设置"]
        )
        
        st.markdown("---")
        
        # 系统状态
        st.subheader("系统状态")
        status = st.session_state.processor.get_system_status()
        
        if status.get('system_ready', False):
            st.success("✅ 系统就绪")
            st.metric("文档数量", status.get('vector_database', {}).get('total_documents', 0))
            st.metric("结构化文件", status.get('structured_data_files', 0))
        else:
            st.warning("⚠️ 系统未就绪")
            st.info("请先上传并处理研报文件")
    
    # 主内容区域
    if page == "系统概览":
        show_overview()
    elif page == "文档管理":
        show_document_management()
    elif page == "智能问答":
        show_chat_interface()
    elif page == "数据分析":
        show_data_analysis()
    elif page == "系统设置":
        show_system_settings()

def show_overview():
    """系统概览页面"""
    st.header("🏠 系统概览")
    
    # 获取系统状态
    status = st.session_state.processor.get_system_status()
    
    # 基本统计
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "文档总数",
            status.get('vector_database', {}).get('total_documents', 0),
            help="向量数据库中的文档片段数量"
        )
    
    with col2:
        st.metric(
            "结构化文件",
            status.get('structured_data_files', 0),
            help="已处理的结构化数据文件数量"
        )
    
    with col3:
        processing_summary = status.get('processing_summary', {})
        avg_quality = processing_summary.get('avg_quality_score', 0)
        st.metric(
            "平均质量评分",
            f"{avg_quality:.2f}",
            help="结构化数据提取的平均质量评分"
        )
    
    with col4:
        companies_count = len(processing_summary.get('companies', []))
        st.metric(
            "覆盖公司数",
            companies_count,
            help="研报覆盖的公司数量"
        )
    
    st.markdown("---")
    
    # 质量分布图表
    if processing_summary.get('quality_distribution'):
        st.subheader("📈 数据质量分布")
        
        quality_dist = processing_summary['quality_distribution']
        
        # 创建饼图
        fig = px.pie(
            values=list(quality_dist.values()),
            names=list(quality_dist.keys()),
            title="结构化数据质量分布",
            color_discrete_map={
                'high': '#28a745',
                'medium': '#ffc107', 
                'low': '#dc3545'
            }
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # 最近处理的文件
    st.subheader("📄 最近处理的文件")
    
    structured_files = list(config.OUTPUT_DIR.glob("structured_data/*.json"))
    if structured_files:
        # 按修改时间排序，取最新的5个
        recent_files = sorted(structured_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]
        
        file_data = []
        for file_path in recent_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if data.get('processing_status') == 'success':
                    report_info = data.get('report_info', {})
                    structured_data = data.get('structured_data', {})
                    
                    file_data.append({
                        '文件名': report_info.get('filename', ''),
                        '公司': report_info.get('company', ''),
                        '日期': report_info.get('date', ''),
                        '机构': report_info.get('institution', ''),
                        '质量评分': structured_data.get('quality_score', 0)
                    })
            except:
                continue
        
        if file_data:
            df = pd.DataFrame(file_data)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("暂无已处理的文件")
    else:
        st.info("暂无已处理的文件")

def show_document_management():
    """文档管理页面"""
    st.header("📁 文档管理")
    
    tab1, tab2, tab3 = st.tabs(["文件上传", "批量处理", "数据导出"])
    
    with tab1:
        st.subheader("上传研报文件")
        
        uploaded_files = st.file_uploader(
            "选择PDF文件",
            type=['pdf'],
            accept_multiple_files=True,
            help="支持批量上传PDF格式的研报文件"
        )
        
        if uploaded_files:
            st.success(f"已选择 {len(uploaded_files)} 个文件")
            
            if st.button("开始处理", type="primary"):
                process_uploaded_files(uploaded_files)
    
    with tab2:
        st.subheader("批量处理现有文件")
        
        data_dir = Path("data")
        if data_dir.exists():
            pdf_files = list(data_dir.glob("*.pdf"))
            st.info(f"在data目录中找到 {len(pdf_files)} 个PDF文件")
            
            if pdf_files and st.button("处理所有文件", type="primary"):
                with st.spinner("正在处理文件..."):
                    results = st.session_state.processor.process_all_pdfs()
                    
                    st.success("处理完成！")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("成功", results['successful'])
                    with col2:
                        st.metric("失败", results['failed'])
                    
                    # 显示处理结果
                    if results['summary']:
                        st.json(results['summary'])
        else:
            st.warning("data目录不存在，请先创建data目录并放入PDF文件")
    
    with tab3:
        st.subheader("数据导出")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("导出Excel", type="primary"):
                with st.spinner("正在导出..."):
                    result = st.session_state.processor.export_structured_data_to_excel()
                    st.success(result)
                    
                    # 提供下载链接
                    excel_file = config.OUTPUT_DIR / "structured_data_export.xlsx"
                    if excel_file.exists():
                        with open(excel_file, 'rb') as f:
                            st.download_button(
                                "下载Excel文件",
                                f.read(),
                                file_name="structured_data_export.xlsx",
                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            )
        
        with col2:
            if st.button("导出JSON", type="secondary"):
                # 导出所有结构化数据为JSON
                structured_files = list(config.OUTPUT_DIR.glob("structured_data/*.json"))
                if structured_files:
                    all_data = []
                    for file_path in structured_files:
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                all_data.append(data)
                        except:
                            continue
                    
                    if all_data:
                        json_str = json.dumps(all_data, ensure_ascii=False, indent=2)
                        st.download_button(
                            "下载JSON文件",
                            json_str,
                            file_name="all_structured_data.json",
                            mime="application/json"
                        )
                        st.success(f"准备导出 {len(all_data)} 条记录")
                    else:
                        st.warning("没有有效数据可导出")
                else:
                    st.warning("没有找到结构化数据文件")

def process_uploaded_files(uploaded_files):
    """处理上传的文件"""
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    results = {'successful': 0, 'failed': 0, 'details': []}
    
    for i, uploaded_file in enumerate(uploaded_files):
        status_text.text(f"正在处理: {uploaded_file.name}")
        
        # 保存临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
            tmp_file.write(uploaded_file.read())
            tmp_path = Path(tmp_file.name)
        
        try:
            # 处理文件
            result = st.session_state.processor.process_single_pdf(tmp_path)
            
            if result and result.get('processing_status') == 'success':
                results['successful'] += 1
                results['details'].append({
                    'filename': uploaded_file.name,
                    'status': 'success',
                    'quality_score': result.get('structured_data', {}).get('quality_score', 0)
                })
            else:
                results['failed'] += 1
                results['details'].append({
                    'filename': uploaded_file.name,
                    'status': 'failed',
                    'error': result.get('error', '未知错误') if result else '处理失败'
                })
        
        except Exception as e:
            results['failed'] += 1
            results['details'].append({
                'filename': uploaded_file.name,
                'status': 'failed',
                'error': str(e)
            })
        
        finally:
            # 删除临时文件
            if tmp_path.exists():
                os.unlink(tmp_path)
        
        # 更新进度
        progress_bar.progress((i + 1) / len(uploaded_files))
    
    status_text.text("处理完成！")
    
    # 显示结果
    col1, col2 = st.columns(2)
    with col1:
        st.metric("成功", results['successful'])
    with col2:
        st.metric("失败", results['failed'])
    
    # 显示详细结果
    if results['details']:
        df = pd.DataFrame(results['details'])
        st.dataframe(df, use_container_width=True)

def show_chat_interface():
    """智能问答界面"""
    st.header("🤖 智能问答")

    # 检查系统状态
    status = st.session_state.processor.get_system_status()
    if not status.get('system_ready', False):
        st.warning("⚠️ 系统未就绪，请先上传并处理研报文件")
        return

    # 问答界面
    col1, col2 = st.columns([3, 1])

    with col1:
        # 聊天历史
        st.subheader("对话历史")

        # 显示聊天历史
        for i, (question, response) in enumerate(st.session_state.chat_history):
            with st.container():
                st.markdown(f"**🙋 用户:** {question}")
                st.markdown(f"**🤖 助手:** {response.answer}")

                # 显示置信度和来源
                col_conf, col_src = st.columns(2)
                with col_conf:
                    st.caption(f"置信度: {response.confidence:.2f}")
                with col_src:
                    st.caption(f"来源: {len(response.sources)} 个文档")

                # 可展开的详细信息
                with st.expander("查看详细信息"):
                    st.markdown(f"**推理过程:** {response.reasoning}")

                    if response.sources:
                        st.markdown("**参考来源:**")
                        for j, source in enumerate(response.sources[:3]):
                            st.markdown(f"{j+1}. {source['content'][:200]}...")
                            st.caption(f"来源: {source['metadata'].get('filename', '未知')}")

                st.markdown("---")

    with col2:
        # 快速问题建议
        st.subheader("💡 问题建议")

        suggested_questions = [
            "太辰光的主要业务是什么？",
            "公司最近的财务表现如何？",
            "AI对公司业务的影响？",
            "分析师的投资建议是什么？",
            "公司面临的主要风险？",
            "未来成长驱动因素？",
            "与同行业公司的比较？",
            "最新的盈利预测？"
        ]

        for question in suggested_questions:
            if st.button(question, key=f"suggest_{hash(question)}"):
                process_question(question)

    # 输入框
    st.markdown("---")
    question = st.text_input(
        "请输入您的问题:",
        placeholder="例如：太辰光的主要业务是什么？",
        key="question_input"
    )

    col_ask, col_clear = st.columns([1, 1])

    with col_ask:
        if st.button("提问", type="primary") and question:
            process_question(question)

    with col_clear:
        if st.button("清空历史"):
            st.session_state.chat_history = []
            st.rerun()

def process_question(question: str):
    """处理用户问题"""
    with st.spinner("正在思考..."):
        response = st.session_state.rag_system.answer_question(question)
        st.session_state.chat_history.append((question, response))
        st.rerun()

def show_data_analysis():
    """数据分析页面"""
    st.header("📊 数据分析")

    # 读取结构化数据
    structured_files = list(config.OUTPUT_DIR.glob("structured_data/*.json"))

    if not structured_files:
        st.warning("暂无结构化数据，请先处理研报文件")
        return

    # 加载数据
    all_data = []
    for file_path in structured_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if data.get('processing_status') == 'success':
                    all_data.append(data)
        except:
            continue

    if not all_data:
        st.warning("没有有效的结构化数据")
        return

    # 分析选项
    analysis_type = st.selectbox(
        "选择分析类型",
        ["质量评分分析", "机构分析", "时间趋势分析", "投资线索分析", "财务预测分析"]
    )

    if analysis_type == "质量评分分析":
        show_quality_analysis(all_data)
    elif analysis_type == "机构分析":
        show_institution_analysis(all_data)
    elif analysis_type == "时间趋势分析":
        show_time_trend_analysis(all_data)
    elif analysis_type == "投资线索分析":
        show_investment_clues_analysis(all_data)
    elif analysis_type == "财务预测分析":
        show_financial_forecast_analysis(all_data)

def show_quality_analysis(all_data: List[Dict]):
    """质量评分分析"""
    st.subheader("📈 质量评分分析")

    # 提取质量评分数据
    quality_data = []
    for data in all_data:
        report_info = data.get('report_info', {})
        structured_data = data.get('structured_data', {})

        quality_data.append({
            'filename': report_info.get('filename', ''),
            'institution': report_info.get('institution', ''),
            'date': report_info.get('date', ''),
            'quality_score': structured_data.get('quality_score', 0)
        })

    df = pd.DataFrame(quality_data)

    # 质量评分分布直方图
    fig_hist = px.histogram(
        df,
        x='quality_score',
        nbins=20,
        title="质量评分分布",
        labels={'quality_score': '质量评分', 'count': '数量'}
    )
    st.plotly_chart(fig_hist, use_container_width=True)

    # 按机构的质量评分
    if len(df['institution'].unique()) > 1:
        fig_box = px.box(
            df,
            x='institution',
            y='quality_score',
            title="各机构质量评分分布"
        )
        fig_box.update_xaxes(tickangle=45)
        st.plotly_chart(fig_box, use_container_width=True)

    # 统计信息
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("平均质量评分", f"{df['quality_score'].mean():.2f}")
    with col2:
        st.metric("最高质量评分", f"{df['quality_score'].max():.2f}")
    with col3:
        st.metric("最低质量评分", f"{df['quality_score'].min():.2f}")

def show_institution_analysis(all_data: List[Dict]):
    """机构分析"""
    st.subheader("🏢 机构分析")

    # 统计各机构的研报数量
    institution_counts = {}
    for data in all_data:
        institution = data.get('report_info', {}).get('institution', '未知')
        institution_counts[institution] = institution_counts.get(institution, 0) + 1

    # 创建柱状图
    fig = px.bar(
        x=list(institution_counts.keys()),
        y=list(institution_counts.values()),
        title="各机构研报数量",
        labels={'x': '机构', 'y': '研报数量'}
    )
    fig.update_xaxes(tickangle=45)
    st.plotly_chart(fig, use_container_width=True)

    # 显示详细统计
    st.subheader("详细统计")
    institution_df = pd.DataFrame([
        {'机构': k, '研报数量': v}
        for k, v in sorted(institution_counts.items(), key=lambda x: x[1], reverse=True)
    ])
    st.dataframe(institution_df, use_container_width=True)

def show_time_trend_analysis(all_data: List[Dict]):
    """时间趋势分析"""
    st.subheader("📅 时间趋势分析")

    # 提取时间数据
    time_data = []
    for data in all_data:
        report_info = data.get('report_info', {})
        date_str = report_info.get('date', '')

        if len(date_str) == 8:  # YYYYMMDD格式
            try:
                year = int(date_str[:4])
                month = int(date_str[4:6])
                time_data.append({
                    'year': year,
                    'month': month,
                    'year_month': f"{year}-{month:02d}",
                    'institution': report_info.get('institution', ''),
                    'quality_score': data.get('structured_data', {}).get('quality_score', 0)
                })
            except:
                continue

    if not time_data:
        st.warning("没有有效的时间数据")
        return

    df = pd.DataFrame(time_data)

    # 按月份统计研报数量
    monthly_counts = df.groupby('year_month').size().reset_index(name='count')
    monthly_counts = monthly_counts.sort_values('year_month')

    fig_line = px.line(
        monthly_counts,
        x='year_month',
        y='count',
        title="研报发布时间趋势",
        labels={'year_month': '年月', 'count': '研报数量'}
    )
    fig_line.update_xaxes(tickangle=45)
    st.plotly_chart(fig_line, use_container_width=True)

    # 质量评分时间趋势
    monthly_quality = df.groupby('year_month')['quality_score'].mean().reset_index()
    monthly_quality = monthly_quality.sort_values('year_month')

    fig_quality = px.line(
        monthly_quality,
        x='year_month',
        y='quality_score',
        title="质量评分时间趋势",
        labels={'year_month': '年月', 'quality_score': '平均质量评分'}
    )
    fig_quality.update_xaxes(tickangle=45)
    st.plotly_chart(fig_quality, use_container_width=True)

def show_investment_clues_analysis(all_data: List[Dict]):
    """投资线索分析"""
    st.subheader("🔍 投资线索分析")

    # 统计投资线索类型
    clue_types = {}
    all_clues = []

    for data in all_data:
        investment_clues = data.get('structured_data', {}).get('investment_clues', [])
        for clue in investment_clues:
            clue_type = clue.get('type', '其他')
            clue_types[clue_type] = clue_types.get(clue_type, 0) + 1
            all_clues.append({
                'type': clue_type,
                'content': clue.get('content', ''),
                'confidence': clue.get('confidence', 0),
                'filename': data.get('report_info', {}).get('filename', '')
            })

    if not clue_types:
        st.warning("没有找到投资线索数据")
        return

    # 投资线索类型分布
    fig_pie = px.pie(
        values=list(clue_types.values()),
        names=list(clue_types.keys()),
        title="投资线索类型分布"
    )
    st.plotly_chart(fig_pie, use_container_width=True)

    # 显示具体投资线索
    st.subheader("具体投资线索")
    clues_df = pd.DataFrame(all_clues)

    # 按类型筛选
    selected_type = st.selectbox("选择线索类型", ["全部"] + list(clue_types.keys()))

    if selected_type != "全部":
        filtered_df = clues_df[clues_df['type'] == selected_type]
    else:
        filtered_df = clues_df

    # 按置信度排序
    filtered_df = filtered_df.sort_values('confidence', ascending=False)

    st.dataframe(
        filtered_df[['type', 'content', 'confidence', 'filename']],
        use_container_width=True
    )

def show_financial_forecast_analysis(all_data: List[Dict]):
    """财务预测分析"""
    st.subheader("💰 财务预测分析")

    # 提取财务预测数据
    forecasts = []
    for data in all_data:
        financial_forecasts = data.get('structured_data', {}).get('financial_forecasts', [])
        filename = data.get('report_info', {}).get('filename', '')

        for forecast in financial_forecasts:
            forecasts.append({
                'filename': filename,
                'metric': forecast.get('metric', ''),
                'period': forecast.get('period', ''),
                'value': forecast.get('value'),
                'unit': forecast.get('unit', ''),
                'change_rate': forecast.get('change_rate')
            })

    if not forecasts:
        st.warning("没有找到财务预测数据")
        return

    df = pd.DataFrame(forecasts)

    # 按指标类型统计
    metric_counts = df['metric'].value_counts()

    fig_bar = px.bar(
        x=metric_counts.index,
        y=metric_counts.values,
        title="财务预测指标分布",
        labels={'x': '指标类型', 'y': '预测数量'}
    )
    fig_bar.update_xaxes(tickangle=45)
    st.plotly_chart(fig_bar, use_container_width=True)

    # 显示具体预测数据
    st.subheader("具体财务预测")

    # 按指标筛选
    selected_metric = st.selectbox("选择指标类型", ["全部"] + list(metric_counts.index))

    if selected_metric != "全部":
        filtered_df = df[df['metric'] == selected_metric]
    else:
        filtered_df = df

    st.dataframe(filtered_df, use_container_width=True)

def show_system_settings():
    """系统设置页面"""
    st.header("⚙️ 系统设置")

    # API配置
    st.subheader("API配置")

    with st.form("api_config"):
        api_key = st.text_input(
            "OpenAI API Key",
            value=config.OPENAI_API_KEY,
            type="password",
            help="用于调用大模型的API密钥"
        )

        base_url = st.text_input(
            "API Base URL",
            value=config.OPENAI_BASE_URL,
            help="API服务的基础URL"
        )

        model = st.text_input(
            "模型名称",
            value=config.OPENAI_MODEL,
            help="使用的大模型名称"
        )

        if st.form_submit_button("保存配置"):
            # 这里可以添加保存配置的逻辑
            st.success("配置已保存（注意：需要重启应用生效）")

    st.markdown("---")

    # 系统维护
    st.subheader("系统维护")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("清空向量数据库", type="secondary"):
            if st.session_state.processor.vector_store.clear_collection():
                st.success("向量数据库已清空")
            else:
                st.error("清空失败")

    with col2:
        if st.button("重建索引", type="secondary"):
            with st.spinner("正在重建索引..."):
                # 重新处理所有文件
                results = st.session_state.processor.process_all_pdfs()
                st.success(f"索引重建完成：成功 {results['successful']}, 失败 {results['failed']}")

    # 系统信息
    st.markdown("---")
    st.subheader("系统信息")

    system_info = {
        "向量数据库路径": str(config.VECTOR_DB_DIR),
        "输出目录": str(config.OUTPUT_DIR),
        "嵌入模型": config.EMBEDDING_MODEL,
        "最大文本块大小": config.MAX_CHUNK_SIZE,
        "检索数量": config.TOP_K_RETRIEVAL
    }

    for key, value in system_info.items():
        st.text(f"{key}: {value}")

if __name__ == "__main__":
    main()
