"""
系统启动脚本
提供命令行接口来运行不同的系统功能
"""
import argparse
import sys
from pathlib import Path
from loguru import logger

from main_processor import MainProcessor
from rag_system import RAGSystem
from config import config

def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stdout,
        level=config.LOG_LEVEL,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    logger.add(
        config.LOG_FILE,
        level=config.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB"
    )

def process_pdfs():
    """处理PDF文件"""
    logger.info("开始处理PDF文件")
    processor = MainProcessor()
    results = processor.process_all_pdfs()
    
    print(f"\n处理结果:")
    print(f"总文件数: {results['total_files']}")
    print(f"成功: {results['successful']}")
    print(f"失败: {results['failed']}")
    
    if results['summary']:
        print(f"\n摘要信息:")
        print(f"平均质量评分: {results['summary']['avg_quality_score']}")
        print(f"质量分布: {results['summary']['quality_distribution']}")
        print(f"涉及公司: {len(results['summary']['companies'])}")
        print(f"涉及机构: {len(results['summary']['institutions'])}")

def export_data():
    """导出数据"""
    logger.info("开始导出数据")
    processor = MainProcessor()
    result = processor.export_structured_data_to_excel()
    print(f"导出结果: {result}")

def test_rag():
    """测试RAG系统"""
    logger.info("开始测试RAG系统")
    rag_system = RAGSystem()
    
    test_questions = [
        "太辰光的主要业务是什么？",
        "公司最近的财务表现如何？",
        "AI对太辰光业务的影响是什么？",
        "分析师对太辰光的投资建议是什么？",
        "公司面临的主要风险有哪些？"
    ]
    
    for question in test_questions:
        print(f"\n问题: {question}")
        print("=" * 50)
        
        response = rag_system.answer_question(question)
        
        print(f"答案: {response.answer}")
        print(f"置信度: {response.confidence}")
        print(f"推理: {response.reasoning}")
        print(f"来源数量: {len(response.sources)}")
        
        if response.sources:
            print("\n主要来源:")
            for i, source in enumerate(response.sources[:2]):
                print(f"{i+1}. {source['content'][:100]}...")
                print(f"   来源: {source['metadata'].get('filename', '未知')}")

def interactive_chat():
    """交互式聊天"""
    logger.info("启动交互式聊天")
    rag_system = RAGSystem()
    
    print("欢迎使用研报RAG问答系统！")
    print("输入 'quit' 或 'exit' 退出")
    print("=" * 50)
    
    while True:
        try:
            question = input("\n请输入您的问题: ").strip()
            
            if question.lower() in ['quit', 'exit', '退出']:
                print("再见！")
                break
            
            if not question:
                continue
            
            print("\n正在思考...")
            response = rag_system.answer_question(question)
            
            print(f"\n答案: {response.answer}")
            print(f"置信度: {response.confidence:.2f}")
            
            if response.sources:
                print(f"\n参考来源 ({len(response.sources)} 个):")
                for i, source in enumerate(response.sources[:3]):
                    print(f"{i+1}. {source['content'][:150]}...")
                    print(f"   文件: {source['metadata'].get('filename', '未知')}")
            
        except KeyboardInterrupt:
            print("\n\n再见！")
            break
        except Exception as e:
            print(f"错误: {e}")

def run_web_app():
    """运行Web应用"""
    import subprocess
    import os
    
    logger.info("启动Web应用")
    print("启动Streamlit Web应用...")
    print("应用将在浏览器中打开: http://localhost:8501")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = str(Path.cwd())
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ], env=env)
    except KeyboardInterrupt:
        print("\n应用已停止")

def check_system():
    """检查系统状态"""
    logger.info("检查系统状态")
    processor = MainProcessor()
    status = processor.get_system_status()
    
    print("系统状态检查:")
    print("=" * 30)
    
    # 检查配置
    print(f"OpenAI API Key: {'已配置' if config.OPENAI_API_KEY else '未配置'}")
    print(f"数据目录: {config.DATA_DIR} ({'存在' if config.DATA_DIR.exists() else '不存在'})")
    print(f"输出目录: {config.OUTPUT_DIR} ({'存在' if config.OUTPUT_DIR.exists() else '不存在'})")
    print(f"向量数据库目录: {config.VECTOR_DB_DIR} ({'存在' if config.VECTOR_DB_DIR.exists() else '不存在'})")
    
    # 检查数据
    if 'vector_database' in status:
        print(f"向量数据库文档数: {status['vector_database'].get('total_documents', 0)}")
    
    print(f"结构化数据文件数: {status.get('structured_data_files', 0)}")
    print(f"系统就绪: {'是' if status.get('system_ready', False) else '否'}")
    
    # 检查PDF文件
    if config.DATA_DIR.exists():
        pdf_files = list(config.DATA_DIR.glob("*.pdf"))
        print(f"待处理PDF文件数: {len(pdf_files)}")
    
    # 给出建议
    print("\n建议:")
    if not config.OPENAI_API_KEY:
        print("- 请配置OpenAI API Key")
    
    if not status.get('system_ready', False):
        print("- 请先处理PDF文件以初始化系统")
    
    if config.DATA_DIR.exists() and list(config.DATA_DIR.glob("*.pdf")):
        print("- 发现未处理的PDF文件，建议运行: python run_system.py --process")

def main():
    """主函数"""
    setup_logging()
    
    parser = argparse.ArgumentParser(description="研报RAG分析系统")
    parser.add_argument("--process", action="store_true", help="处理PDF文件")
    parser.add_argument("--export", action="store_true", help="导出结构化数据")
    parser.add_argument("--test", action="store_true", help="测试RAG系统")
    parser.add_argument("--chat", action="store_true", help="启动交互式聊天")
    parser.add_argument("--web", action="store_true", help="启动Web应用")
    parser.add_argument("--check", action="store_true", help="检查系统状态")
    
    args = parser.parse_args()
    
    if args.process:
        process_pdfs()
    elif args.export:
        export_data()
    elif args.test:
        test_rag()
    elif args.chat:
        interactive_chat()
    elif args.web:
        run_web_app()
    elif args.check:
        check_system()
    else:
        print("研报RAG分析系统")
        print("=" * 30)
        print("可用命令:")
        print("  --process  处理PDF文件")
        print("  --export   导出结构化数据")
        print("  --test     测试RAG系统")
        print("  --chat     启动交互式聊天")
        print("  --web      启动Web应用")
        print("  --check    检查系统状态")
        print("\n示例:")
        print("  python run_system.py --check")
        print("  python run_system.py --process")
        print("  python run_system.py --web")

if __name__ == "__main__":
    main()
