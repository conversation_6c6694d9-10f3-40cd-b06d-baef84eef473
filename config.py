"""
研报RAG系统配置文件
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """系统配置类"""
    
    # 项目路径
    PROJECT_ROOT = Path(__file__).parent
    DATA_DIR = PROJECT_ROOT / "data"
    OUTPUT_DIR = PROJECT_ROOT / "output"
    VECTOR_DB_DIR = PROJECT_ROOT / "vector_db"
    
    # 确保目录存在
    OUTPUT_DIR.mkdir(exist_ok=True)
    VECTOR_DB_DIR.mkdir(exist_ok=True)
    
    # OpenAI配置
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
    OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4")
    
    # 向量数据库配置
    EMBEDDING_MODEL = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
    VECTOR_DB_COLLECTION = "research_reports"
    
    # 文本处理配置
    MAX_CHUNK_SIZE = 1000
    CHUNK_OVERLAP = 200
    
    # RAG配置
    TOP_K_RETRIEVAL = 5
    SIMILARITY_THRESHOLD = 0.7
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FILE = PROJECT_ROOT / "logs" / "system.log"
    
    # 确保日志目录存在
    LOG_FILE.parent.mkdir(exist_ok=True)

# 全局配置实例
config = Config()
