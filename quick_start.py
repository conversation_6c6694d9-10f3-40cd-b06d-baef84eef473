"""
快速启动脚本
一键完成系统初始化和启动
"""
import os
import sys
import subprocess
from pathlib import Path
import time

def print_banner():
    """打印欢迎横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                     研报RAG分析系统                          ║
    ║                  Research Report RAG System                  ║
    ║                                                              ║
    ║  🚀 一键启动 | 📊 智能分析 | 🤖 AI问答 | 📈 数据可视化      ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ 错误: requirements.txt文件不存在")
        return False
    
    try:
        # 检查是否安装了pip
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        
        # 尝试导入关键包
        critical_packages = ['streamlit', 'openai', 'pandas', 'chromadb']
        missing_packages = []
        
        for package in critical_packages:
            try:
                __import__(package)
                print(f"   ✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"   ❌ {package} (未安装)")
        
        if missing_packages:
            print(f"\n⚠️  发现 {len(missing_packages)} 个缺失的依赖包")
            install = input("是否自动安装依赖包? (y/n): ").lower().strip()
            
            if install == 'y':
                print("📥 正在安装依赖包...")
                try:
                    subprocess.run([
                        sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
                    ], check=True)
                    print("✅ 依赖包安装完成")
                    return True
                except subprocess.CalledProcessError:
                    print("❌ 依赖包安装失败")
                    return False
            else:
                print("❌ 请手动安装依赖包: pip install -r requirements.txt")
                return False
        else:
            print("✅ 所有关键依赖包已安装")
            return True
            
    except subprocess.CalledProcessError:
        print("❌ 错误: pip未正确安装")
        return False

def check_api_config():
    """检查API配置"""
    print("\n🔑 检查API配置...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("⚠️  .env文件不存在，正在创建...")
            env_file.write_text(env_example.read_text(encoding='utf-8'), encoding='utf-8')
            print("✅ 已创建.env文件")
        else:
            print("❌ 错误: .env.example文件不存在")
            return False
    
    # 检查API密钥
    from config import config
    if not config.OPENAI_API_KEY:
        print("⚠️  OpenAI API Key未配置")
        print("   请编辑.env文件，设置OPENAI_API_KEY")
        
        api_key = input("请输入您的OpenAI API Key (或按Enter跳过): ").strip()
        if api_key:
            # 更新.env文件
            env_content = env_file.read_text(encoding='utf-8')
            if 'OPENAI_API_KEY=' in env_content:
                env_content = env_content.replace(
                    'OPENAI_API_KEY=your_openai_api_key_here',
                    f'OPENAI_API_KEY={api_key}'
                )
            else:
                env_content += f'\nOPENAI_API_KEY={api_key}\n'
            
            env_file.write_text(env_content, encoding='utf-8')
            print("✅ API Key已保存")
            return True
        else:
            print("⚠️  跳过API配置，部分功能可能无法使用")
            return False
    else:
        print("✅ API配置检查通过")
        return True

def check_data_directory():
    """检查数据目录"""
    print("\n📁 检查数据目录...")
    
    data_dir = Path("data")
    if not data_dir.exists():
        print("📁 创建data目录...")
        data_dir.mkdir()
        print("✅ data目录已创建")
    
    pdf_files = list(data_dir.glob("*.pdf"))
    if pdf_files:
        print(f"✅ 找到 {len(pdf_files)} 个PDF文件")
        return True
    else:
        print("⚠️  data目录中没有PDF文件")
        print("   请将研报PDF文件放入data目录")
        return False

def initialize_system():
    """初始化系统"""
    print("\n🔧 初始化系统...")
    
    try:
        # 运行系统检查
        from test_system import run_system_check
        run_system_check()
        return True
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return False

def process_documents():
    """处理文档"""
    print("\n📄 处理研报文档...")
    
    data_dir = Path("data")
    pdf_files = list(data_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("⚠️  没有找到PDF文件，跳过文档处理")
        return True
    
    process = input(f"发现 {len(pdf_files)} 个PDF文件，是否现在处理? (y/n): ").lower().strip()
    
    if process == 'y':
        try:
            print("🔄 正在处理文档...")
            result = subprocess.run([
                sys.executable, "run_system.py", "--process"
            ], capture_output=True, text=True, timeout=300)  # 5分钟超时
            
            if result.returncode == 0:
                print("✅ 文档处理完成")
                return True
            else:
                print(f"❌ 文档处理失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("⚠️  文档处理超时，请稍后手动运行")
            return False
        except Exception as e:
            print(f"❌ 文档处理出错: {e}")
            return False
    else:
        print("⚠️  跳过文档处理，可稍后手动运行")
        return True

def start_web_app():
    """启动Web应用"""
    print("\n🌐 启动Web应用...")
    print("   应用将在浏览器中打开: http://localhost:8501")
    print("   按 Ctrl+C 停止应用")
    
    try:
        time.sleep(2)  # 给用户时间阅读信息
        subprocess.run([
            sys.executable, "run_system.py", "--web"
        ])
    except KeyboardInterrupt:
        print("\n👋 应用已停止，感谢使用！")
    except Exception as e:
        print(f"❌ 启动Web应用失败: {e}")

def show_usage_guide():
    """显示使用指南"""
    guide = """
    📖 使用指南:
    
    1. 文档管理:
       - 上传PDF研报文件
       - 批量处理现有文件
       - 导出结构化数据
    
    2. 智能问答:
       - 询问公司基本信息
       - 查询财务数据
       - 了解投资建议
       - 分析风险因素
    
    3. 数据分析:
       - 质量评分分析
       - 机构研报统计
       - 时间趋势分析
       - 投资线索挖掘
    
    4. 命令行工具:
       - python run_system.py --check    # 检查系统状态
       - python run_system.py --process  # 处理PDF文件
       - python run_system.py --chat     # 命令行问答
       - python run_system.py --export   # 导出数据
    
    💡 提示: 首次使用建议先处理几个PDF文件，然后体验问答功能
    """
    print(guide)

def main():
    """主函数"""
    print_banner()
    
    # 系统检查步骤
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("API配置", check_api_config),
        ("数据目录", check_data_directory),
    ]
    
    print("🔍 开始系统检查...\n")
    
    all_passed = True
    for name, check_func in checks:
        if not check_func():
            all_passed = False
            break
    
    if not all_passed:
        print("\n❌ 系统检查未通过，请解决上述问题后重新运行")
        return
    
    print("\n✅ 系统检查通过！")
    
    # 初始化系统
    if not initialize_system():
        print("\n❌ 系统初始化失败")
        return
    
    # 处理文档
    process_documents()
    
    # 显示使用指南
    show_usage_guide()
    
    # 启动Web应用
    start_choice = input("\n🚀 是否现在启动Web应用? (y/n): ").lower().strip()
    
    if start_choice == 'y':
        start_web_app()
    else:
        print("\n👋 设置完成！")
        print("   运行 'python run_system.py --web' 启动Web应用")
        print("   运行 'python run_system.py --help' 查看更多选项")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消，再见！")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        print("请检查系统环境或联系技术支持")
