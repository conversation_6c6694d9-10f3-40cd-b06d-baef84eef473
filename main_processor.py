"""
主要数据处理模块
整合PDF解析、结构化提取、向量存储等功能
"""
import json
from pathlib import Path
from typing import List, Dict, Optional
from dataclasses import asdict
from loguru import logger
from tqdm import tqdm
import pandas as pd

from pdf_parser import PDFParser, ResearchReport
from structured_extractor import StructuredExtractor, StructuredData
from vector_store import VectorStore
from rag_system import RAGSystem
from config import config

class MainProcessor:
    """主处理器"""
    
    def __init__(self):
        self.pdf_parser = PDFParser()
        self.structured_extractor = StructuredExtractor()
        self.vector_store = VectorStore()
        self.rag_system = RAGSystem()
        
        # 确保输出目录存在
        self.structured_data_dir = config.OUTPUT_DIR / "structured_data"
        self.reports_dir = config.OUTPUT_DIR / "reports"
        self.structured_data_dir.mkdir(exist_ok=True)
        self.reports_dir.mkdir(exist_ok=True)
    
    def process_single_pdf(self, pdf_path: Path) -> Optional[Dict]:
        """处理单个PDF文件"""
        logger.info(f"开始处理PDF: {pdf_path.name}")
        
        try:
            # 1. 解析PDF
            report = self.pdf_parser.parse_pdf(pdf_path)
            if not report:
                logger.warning(f"PDF解析失败: {pdf_path.name}")
                return None
            
            # 2. 提取结构化数据
            structured_data = self.structured_extractor.extract_structured_data(report.content)
            if not structured_data:
                logger.warning(f"结构化数据提取失败: {pdf_path.name}")
                # 即使结构化提取失败，也要保存基本信息
                structured_data = StructuredData(
                    investment_clues=[],
                    investment_logic=[],
                    key_viewpoints=[],
                    financial_forecasts=[],
                    valuation_info=[],
                    growth_drivers=[],
                    risk_factors=[],
                    quality_score=0.1
                )
            
            # 3. 添加到向量数据库
            metadata = {
                'filename': report.filename,
                'title': report.title,
                'company': report.company,
                'stock_code': report.stock_code,
                'date': report.date,
                'institution': report.institution,
                'analyst': report.analyst,
                'quality_score': structured_data.quality_score
            }
            
            doc_ids = self.vector_store.add_document(report.content, metadata)
            
            # 4. 保存结构化数据
            result = {
                'report_info': asdict(report),
                'structured_data': asdict(structured_data),
                'vector_doc_ids': doc_ids,
                'processing_status': 'success'
            }
            
            # 保存到文件
            output_file = self.structured_data_dir / f"{pdf_path.stem}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"成功处理PDF: {pdf_path.name}, 质量评分: {structured_data.quality_score}")
            return result
            
        except Exception as e:
            logger.error(f"处理PDF失败: {pdf_path.name}, 错误: {e}")
            return {
                'filename': pdf_path.name,
                'processing_status': 'failed',
                'error': str(e)
            }
    
    def process_all_pdfs(self, data_dir: Path = None) -> Dict:
        """处理所有PDF文件"""
        if data_dir is None:
            data_dir = config.DATA_DIR
        
        if not data_dir.exists():
            logger.error(f"数据目录不存在: {data_dir}")
            return {'status': 'error', 'message': '数据目录不存在'}
        
        pdf_files = list(data_dir.glob("*.pdf"))
        logger.info(f"找到 {len(pdf_files)} 个PDF文件")
        
        if not pdf_files:
            return {'status': 'error', 'message': '未找到PDF文件'}
        
        results = {
            'total_files': len(pdf_files),
            'successful': 0,
            'failed': 0,
            'results': [],
            'summary': {}
        }
        
        # 处理每个PDF文件
        for pdf_file in tqdm(pdf_files, desc="处理PDF文件"):
            result = self.process_single_pdf(pdf_file)
            if result:
                results['results'].append(result)
                if result.get('processing_status') == 'success':
                    results['successful'] += 1
                else:
                    results['failed'] += 1
        
        # 生成处理摘要
        results['summary'] = self.generate_processing_summary(results['results'])
        
        # 保存处理结果
        summary_file = config.OUTPUT_DIR / "processing_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"处理完成: 成功 {results['successful']}, 失败 {results['failed']}")
        return results
    
    def generate_processing_summary(self, results: List[Dict]) -> Dict:
        """生成处理摘要"""
        summary = {
            'quality_distribution': {},
            'companies': set(),
            'institutions': set(),
            'date_range': {'earliest': None, 'latest': None},
            'avg_quality_score': 0.0
        }
        
        quality_scores = []
        
        for result in results:
            if result.get('processing_status') == 'success':
                structured_data = result.get('structured_data', {})
                report_info = result.get('report_info', {})
                
                # 质量评分统计
                quality_score = structured_data.get('quality_score', 0)
                quality_scores.append(quality_score)
                
                # 质量分布
                if quality_score >= 0.8:
                    quality_level = 'high'
                elif quality_score >= 0.5:
                    quality_level = 'medium'
                else:
                    quality_level = 'low'
                
                summary['quality_distribution'][quality_level] = \
                    summary['quality_distribution'].get(quality_level, 0) + 1
                
                # 公司和机构统计
                summary['companies'].add(report_info.get('company', ''))
                summary['institutions'].add(report_info.get('institution', ''))
                
                # 日期范围
                date = report_info.get('date', '')
                if date:
                    if not summary['date_range']['earliest'] or date < summary['date_range']['earliest']:
                        summary['date_range']['earliest'] = date
                    if not summary['date_range']['latest'] or date > summary['date_range']['latest']:
                        summary['date_range']['latest'] = date
        
        # 计算平均质量评分
        if quality_scores:
            summary['avg_quality_score'] = round(sum(quality_scores) / len(quality_scores), 2)
        
        # 转换集合为列表
        summary['companies'] = list(summary['companies'])
        summary['institutions'] = list(summary['institutions'])
        
        return summary
    
    def export_structured_data_to_excel(self, output_file: Path = None) -> str:
        """导出结构化数据到Excel"""
        if output_file is None:
            output_file = config.OUTPUT_DIR / "structured_data_export.xlsx"
        
        # 读取所有结构化数据
        structured_files = list(self.structured_data_dir.glob("*.json"))
        
        if not structured_files:
            logger.warning("没有找到结构化数据文件")
            return "没有数据可导出"
        
        # 准备数据
        export_data = []
        
        for file_path in structured_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if data.get('processing_status') == 'success':
                    report_info = data.get('report_info', {})
                    structured_data = data.get('structured_data', {})
                    
                    # 基本信息
                    row = {
                        '文件名': report_info.get('filename', ''),
                        '标题': report_info.get('title', ''),
                        '公司': report_info.get('company', ''),
                        '股票代码': report_info.get('stock_code', ''),
                        '日期': report_info.get('date', ''),
                        '机构': report_info.get('institution', ''),
                        '分析师': report_info.get('analyst', ''),
                        '质量评分': structured_data.get('quality_score', 0),
                    }
                    
                    # 投资线索
                    investment_clues = structured_data.get('investment_clues', [])
                    row['投资线索数量'] = len(investment_clues)
                    row['投资线索'] = '; '.join([clue.get('content', '') for clue in investment_clues[:3]])
                    
                    # 关键观点
                    key_viewpoints = structured_data.get('key_viewpoints', [])
                    row['关键观点'] = '; '.join(key_viewpoints[:3])
                    
                    # 财务预测
                    financial_forecasts = structured_data.get('financial_forecasts', [])
                    row['财务预测数量'] = len(financial_forecasts)
                    
                    # 成长驱动因素
                    growth_drivers = structured_data.get('growth_drivers', [])
                    row['成长驱动因素'] = '; '.join(growth_drivers[:3])
                    
                    export_data.append(row)
                    
            except Exception as e:
                logger.error(f"读取文件失败: {file_path}, 错误: {e}")
        
        # 创建DataFrame并导出
        if export_data:
            df = pd.DataFrame(export_data)
            df.to_excel(output_file, index=False, engine='openpyxl')
            logger.info(f"成功导出 {len(export_data)} 条记录到 {output_file}")
            return f"成功导出 {len(export_data)} 条记录"
        else:
            return "没有有效数据可导出"
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        try:
            # 向量数据库统计
            vector_stats = self.vector_store.get_collection_stats()
            
            # 结构化数据统计
            structured_files = list(self.structured_data_dir.glob("*.json"))
            
            # 处理摘要
            summary_file = config.OUTPUT_DIR / "processing_summary.json"
            processing_summary = {}
            if summary_file.exists():
                with open(summary_file, 'r', encoding='utf-8') as f:
                    processing_summary = json.load(f)
            
            return {
                'vector_database': vector_stats,
                'structured_data_files': len(structured_files),
                'processing_summary': processing_summary.get('summary', {}),
                'system_ready': len(structured_files) > 0 and vector_stats.get('total_documents', 0) > 0
            }
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {'error': str(e)}

    def test_rag_system(self) -> None:
        """测试RAG系统"""
        test_questions = [
            "太辰光的主要业务是什么？",
            "公司最近的财务表现如何？",
            "AI对太辰光业务的影响是什么？",
            "分析师对太辰光的投资建议是什么？"
        ]

        print("\n测试RAG问答系统:")
        for question in test_questions:
            print(f"\n问题: {question}")
            response = self.rag_system.answer_question(question)
            print(f"答案: {response.answer[:200]}...")
            print(f"置信度: {response.confidence}")
            print(f"来源数量: {len(response.sources)}")

def main():
    """主函数"""
    processor = MainProcessor()

    # 处理所有PDF文件
    print("开始处理PDF文件...")
    results = processor.process_all_pdfs()

    print(f"\n处理结果:")
    print(f"总文件数: {results['total_files']}")
    print(f"成功: {results['successful']}")
    print(f"失败: {results['failed']}")

    if results['summary']:
        print(f"\n摘要信息:")
        print(f"平均质量评分: {results['summary']['avg_quality_score']}")
        print(f"质量分布: {results['summary']['quality_distribution']}")
        print(f"涉及公司: {len(results['summary']['companies'])}")
        print(f"涉及机构: {len(results['summary']['institutions'])}")

    # 导出Excel
    print("\n导出结构化数据...")
    export_result = processor.export_structured_data_to_excel()
    print(export_result)

    # 测试RAG系统
    processor.test_rag_system()

    # 获取系统状态
    print("\n系统状态:")
    status = processor.get_system_status()
    print(json.dumps(status, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
