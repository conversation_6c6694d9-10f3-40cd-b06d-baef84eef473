"""
系统测试脚本
验证各个模块的功能是否正常
"""
import unittest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch

from pdf_parser import PDFParser, ResearchReport
from structured_extractor import StructuredExtractor, StructuredData
from vector_store import VectorStore, Document
from rag_system import RAGSystem
from main_processor import MainProcessor
from config import config

class TestPDFParser(unittest.TestCase):
    """测试PDF解析器"""
    
    def setUp(self):
        self.parser = PDFParser()
    
    def test_parse_filename(self):
        """测试文件名解析"""
        filename = "20231027-民生证券-太辰光-300570-2023年三季报点评：营收平稳提升.pdf"
        result = self.parser.parse_filename(filename)
        
        self.assertEqual(result['date'], '20231027')
        self.assertEqual(result['institution'], '民生证券')
        self.assertEqual(result['company'], '太辰光')
        self.assertEqual(result['stock_code'], '300570')
        self.assertIn('2023年三季报点评', result['title'])
    
    def test_clean_text(self):
        """测试文本清理"""
        dirty_text = "  这是一段  测试文本  \n\n  包含多余空格  "
        clean_text = self.parser.clean_text(dirty_text)
        
        self.assertNotIn('  ', clean_text)  # 不应包含多余空格
        self.assertTrue(len(clean_text) > 0)

class TestStructuredExtractor(unittest.TestCase):
    """测试结构化数据提取器"""
    
    def setUp(self):
        self.extractor = StructuredExtractor()
    
    @patch('structured_extractor.OpenAI')
    def test_extract_with_llm_mock(self, mock_openai):
        """测试大模型提取（使用Mock）"""
        # 模拟API响应
        mock_response = Mock()
        mock_response.choices[0].message.content = '''
        {
            "investment_clues": [
                {"type": "盈利预期", "content": "营收增长25%", "confidence": 0.9}
            ],
            "investment_logic": [
                {"logic": "AI需求驱动增长", "supporting_evidence": ["订单增加"], "impact_assessment": "正面"}
            ],
            "key_viewpoints": ["公司业绩超预期"],
            "financial_forecasts": [],
            "valuation_info": [],
            "growth_drivers": ["AI需求"],
            "risk_factors": ["市场竞争"]
        }
        '''
        
        mock_client = Mock()
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai.return_value = mock_client
        
        # 重新初始化提取器以使用Mock
        self.extractor.client = mock_client
        
        result = self.extractor.extract_with_llm("测试内容")
        
        self.assertIsNotNone(result)
        self.assertIn('investment_clues', result)
        self.assertIn('investment_logic', result)
    
    def test_calculate_quality_score(self):
        """测试质量评分计算"""
        # 创建测试数据
        investment_clues = [Mock(), Mock(), Mock()]  # 3个投资线索
        investment_logic = [Mock(), Mock()]  # 2个投资逻辑
        key_viewpoints = ["观点1", "观点2", "观点3"]  # 3个观点
        financial_forecasts = [Mock()]  # 1个财务预测
        valuation_info = [Mock()]  # 1个估值信息
        
        score = self.extractor.calculate_quality_score(
            investment_clues, investment_logic, key_viewpoints,
            financial_forecasts, valuation_info
        )
        
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 1.0)
        self.assertEqual(score, 1.0)  # 所有项目都满足，应该是满分

class TestVectorStore(unittest.TestCase):
    """测试向量数据库"""
    
    def setUp(self):
        # 使用临时目录
        self.temp_dir = tempfile.mkdtemp()
        # 暂时跳过向量数据库测试，因为需要下载模型
        self.skipTest("跳过向量数据库测试，需要下载嵌入模型")
    
    def test_text_splitter(self):
        """测试文本分割器"""
        from vector_store import TextSplitter
        
        splitter = TextSplitter(chunk_size=100, chunk_overlap=20)
        
        long_text = "这是一段很长的文本。" * 50  # 创建长文本
        chunks = splitter.split_text(long_text)
        
        self.assertGreater(len(chunks), 1)  # 应该被分割成多个块
        for chunk in chunks:
            self.assertLessEqual(len(chunk), 120)  # 每个块不应超过限制

class TestRAGSystem(unittest.TestCase):
    """测试RAG系统"""
    
    def setUp(self):
        # 暂时跳过RAG系统测试，因为需要API密钥和向量数据库
        self.skipTest("跳过RAG系统测试，需要API密钥和数据")

class TestMainProcessor(unittest.TestCase):
    """测试主处理器"""
    
    def setUp(self):
        self.processor = MainProcessor()
    
    def test_generate_processing_summary(self):
        """测试处理摘要生成"""
        # 创建测试结果数据
        test_results = [
            {
                'processing_status': 'success',
                'structured_data': {'quality_score': 0.8},
                'report_info': {
                    'company': '太辰光',
                    'institution': '民生证券',
                    'date': '20231027'
                }
            },
            {
                'processing_status': 'success',
                'structured_data': {'quality_score': 0.6},
                'report_info': {
                    'company': '太辰光',
                    'institution': '华泰证券',
                    'date': '20240328'
                }
            }
        ]
        
        summary = self.processor.generate_processing_summary(test_results)
        
        self.assertIn('quality_distribution', summary)
        self.assertIn('companies', summary)
        self.assertIn('institutions', summary)
        self.assertIn('avg_quality_score', summary)
        
        self.assertEqual(len(summary['companies']), 1)  # 只有一个公司
        self.assertEqual(len(summary['institutions']), 2)  # 两个机构
        self.assertEqual(summary['avg_quality_score'], 0.7)  # 平均分

class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_config_loading(self):
        """测试配置加载"""
        self.assertTrue(config.PROJECT_ROOT.exists())
        self.assertTrue(config.OUTPUT_DIR.exists())
        self.assertTrue(config.VECTOR_DB_DIR.exists())
    
    def test_directory_structure(self):
        """测试目录结构"""
        required_files = [
            'config.py',
            'pdf_parser.py',
            'structured_extractor.py',
            'vector_store.py',
            'rag_system.py',
            'main_processor.py',
            'streamlit_app.py',
            'run_system.py',
            'requirements.txt'
        ]
        
        for file_name in required_files:
            file_path = config.PROJECT_ROOT / file_name
            self.assertTrue(file_path.exists(), f"缺少文件: {file_name}")

def run_system_check():
    """运行系统检查"""
    print("=" * 50)
    print("研报RAG系统 - 系统检查")
    print("=" * 50)
    
    # 检查配置
    print("\n1. 配置检查:")
    print(f"   ✓ 项目根目录: {config.PROJECT_ROOT}")
    print(f"   ✓ 数据目录: {config.DATA_DIR} ({'存在' if config.DATA_DIR.exists() else '不存在'})")
    print(f"   ✓ 输出目录: {config.OUTPUT_DIR}")
    print(f"   ✓ 向量数据库目录: {config.VECTOR_DB_DIR}")
    print(f"   {'✓' if config.OPENAI_API_KEY else '✗'} OpenAI API Key: {'已配置' if config.OPENAI_API_KEY else '未配置'}")
    
    # 检查依赖
    print("\n2. 依赖检查:")
    required_packages = [
        'fastapi', 'streamlit', 'pandas', 'numpy', 'openai',
        'chromadb', 'sentence_transformers', 'PyPDF2', 'pdfplumber',
        'fitz', 'jieba', 'loguru', 'tqdm', 'plotly'
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✓ {package}")
        except ImportError:
            print(f"   ✗ {package} (未安装)")
    
    # 检查数据文件
    print("\n3. 数据文件检查:")
    if config.DATA_DIR.exists():
        pdf_files = list(config.DATA_DIR.glob("*.pdf"))
        print(f"   ✓ 找到 {len(pdf_files)} 个PDF文件")
        
        if pdf_files:
            print("   前5个文件:")
            for i, pdf_file in enumerate(pdf_files[:5]):
                print(f"     {i+1}. {pdf_file.name}")
    else:
        print("   ✗ data目录不存在")
    
    # 检查处理结果
    print("\n4. 处理结果检查:")
    structured_files = list(config.OUTPUT_DIR.glob("structured_data/*.json"))
    print(f"   {'✓' if structured_files else '✗'} 结构化数据文件: {len(structured_files)} 个")
    
    # 系统建议
    print("\n5. 系统建议:")
    if not config.OPENAI_API_KEY:
        print("   - 请配置OpenAI API Key")
    
    if not config.DATA_DIR.exists() or not list(config.DATA_DIR.glob("*.pdf")):
        print("   - 请在data目录中放入PDF研报文件")
    
    if not structured_files:
        print("   - 请运行 'python run_system.py --process' 处理PDF文件")
    
    if config.OPENAI_API_KEY and structured_files:
        print("   - 系统已就绪，可以运行 'python run_system.py --web' 启动Web界面")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--check":
        run_system_check()
    else:
        # 运行单元测试
        print("运行单元测试...")
        unittest.main(verbosity=2)
