"""
PDF研报解析模块
支持多种PDF解析方法，提取文本、表格和元数据
"""
import re
import fitz  # PyMuPDF
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from loguru import logger
import pandas as pd

# 尝试导入pdfplumber，如果失败则只使用PyMuPDF
try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False
    print("警告: pdfplumber不可用，将只使用PyMuPDF进行PDF解析")

@dataclass
class ResearchReport:
    """研报数据结构"""
    filename: str
    title: str
    company: str
    stock_code: str
    date: str
    institution: str
    analyst: str
    content: str
    tables: List[Dict]
    metadata: Dict

class PDFParser:
    """PDF解析器"""
    
    def __init__(self):
        self.supported_formats = ['.pdf']
    
    def parse_filename(self, filename: str) -> Dict[str, str]:
        """
        从文件名解析基本信息
        格式: 20231027-民生证券-太辰光-300570-2023年三季报点评：营收平稳提升，光互联领域实力突出成长动力足.pdf
        """
        try:
            # 移除.pdf扩展名
            name = filename.replace('.pdf', '')
            parts = name.split('-')
            
            if len(parts) >= 5:
                date = parts[0]
                institution = parts[1]
                company = parts[2]
                stock_code = parts[3]
                title = '-'.join(parts[4:])
                
                return {
                    'date': date,
                    'institution': institution,
                    'company': company,
                    'stock_code': stock_code,
                    'title': title
                }
        except Exception as e:
            logger.warning(f"解析文件名失败: {filename}, 错误: {e}")
        
        return {
            'date': '',
            'institution': '',
            'company': '',
            'stock_code': '',
            'title': filename
        }
    
    def extract_text_pymupdf(self, pdf_path: Path) -> str:
        """使用PyMuPDF提取文本"""
        try:
            doc = fitz.open(pdf_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
            return text
        except Exception as e:
            logger.error(f"PyMuPDF提取文本失败: {e}")
            return ""
    
    def extract_text_pdfplumber(self, pdf_path: Path) -> Tuple[str, List[Dict]]:
        """使用pdfplumber提取文本和表格"""
        if not PDFPLUMBER_AVAILABLE:
            logger.warning("pdfplumber不可用，返回空结果")
            return "", []

        text = ""
        tables = []

        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # 提取文本
                    page_text = page.extract_text()
                    if page_text:
                        text += f"\n--- 第{page_num + 1}页 ---\n"
                        text += page_text

                    # 提取表格
                    page_tables = page.extract_tables()
                    for table_num, table in enumerate(page_tables):
                        if table:
                            tables.append({
                                'page': page_num + 1,
                                'table_num': table_num + 1,
                                'data': table
                            })
        except Exception as e:
            logger.error(f"pdfplumber提取失败: {e}")

        return text, tables
    
    def clean_text(self, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符
        text = re.sub(r'[^\u4e00-\u9fff\w\s\.\,\;\:\!\?\(\)\[\]\-\+\=\%\$\￥]', '', text)
        
        # 移除过短的行
        lines = text.split('\n')
        cleaned_lines = [line.strip() for line in lines if len(line.strip()) > 3]
        
        return '\n'.join(cleaned_lines)
    
    def extract_analyst_info(self, text: str) -> str:
        """提取分析师信息"""
        patterns = [
            r'分析师[：:]\s*([^\n\r]+)',
            r'研究员[：:]\s*([^\n\r]+)',
            r'分析师\s+([^\n\r]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        return ""
    
    def parse_pdf(self, pdf_path: Path) -> Optional[ResearchReport]:
        """解析PDF文件"""
        try:
            logger.info(f"开始解析PDF: {pdf_path}")
            
            # 解析文件名
            filename_info = self.parse_filename(pdf_path.name)
            
            # 提取文本和表格
            text, tables = self.extract_text_pdfplumber(pdf_path)
            
            # 如果pdfplumber失败，尝试PyMuPDF
            if not text:
                text = self.extract_text_pymupdf(pdf_path)
                tables = []
            
            # 清理文本
            cleaned_text = self.clean_text(text)
            
            if not cleaned_text:
                logger.warning(f"无法提取文本内容: {pdf_path}")
                return None
            
            # 提取分析师信息
            analyst = self.extract_analyst_info(cleaned_text)
            
            # 创建研报对象
            report = ResearchReport(
                filename=pdf_path.name,
                title=filename_info['title'],
                company=filename_info['company'],
                stock_code=filename_info['stock_code'],
                date=filename_info['date'],
                institution=filename_info['institution'],
                analyst=analyst,
                content=cleaned_text,
                tables=tables,
                metadata={
                    'file_size': pdf_path.stat().st_size,
                    'page_count': len(text.split('--- 第')) - 1 if '--- 第' in text else 1,
                    'table_count': len(tables)
                }
            )
            
            logger.info(f"成功解析PDF: {pdf_path.name}")
            return report
            
        except Exception as e:
            logger.error(f"解析PDF失败: {pdf_path}, 错误: {e}")
            return None

def main():
    """测试函数"""
    parser = PDFParser()
    data_dir = Path("data")
    
    if not data_dir.exists():
        print("data目录不存在")
        return
    
    pdf_files = list(data_dir.glob("*.pdf"))
    print(f"找到{len(pdf_files)}个PDF文件")
    
    for pdf_file in pdf_files[:2]:  # 测试前2个文件
        report = parser.parse_pdf(pdf_file)
        if report:
            print(f"\n文件: {report.filename}")
            print(f"标题: {report.title}")
            print(f"公司: {report.company}")
            print(f"股票代码: {report.stock_code}")
            print(f"日期: {report.date}")
            print(f"机构: {report.institution}")
            print(f"分析师: {report.analyst}")
            print(f"内容长度: {len(report.content)}")
            print(f"表格数量: {len(report.tables)}")

if __name__ == "__main__":
    main()
