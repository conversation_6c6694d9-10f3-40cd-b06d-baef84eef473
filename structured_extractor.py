"""
结构化数据提取模块
使用大模型从研报中提取投资线索、投资逻辑、观点等结构化信息
"""
import json
import re
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from openai import OpenAI
from loguru import logger
from config import config

@dataclass
class InvestmentClue:
    """投资线索"""
    type: str  # 盈利预期、产品份额、技术创新等
    content: str
    confidence: float  # 置信度 0-1

@dataclass
class InvestmentLogic:
    """投资逻辑"""
    logic: str
    supporting_evidence: List[str]
    impact_assessment: str  # 正面/负面/中性

@dataclass
class FinancialForecast:
    """财务预测"""
    metric: str  # 营收、净利润、毛利率等
    period: str  # 2024年、2024Q1等
    value: Optional[float]
    unit: str  # 亿元、%等
    change_rate: Optional[float]  # 同比增长率

@dataclass
class ValuationInfo:
    """估值信息"""
    method: str  # PE、PB、DCF等
    value: Optional[float]
    target_price: Optional[float]
    rating: str  # 买入、持有、卖出

@dataclass
class StructuredData:
    """结构化数据"""
    investment_clues: List[InvestmentClue]
    investment_logic: List[InvestmentLogic]
    key_viewpoints: List[str]
    financial_forecasts: List[FinancialForecast]
    valuation_info: List[ValuationInfo]
    growth_drivers: List[str]
    risk_factors: List[str]
    quality_score: float  # 数据质量评分 0-1

class StructuredExtractor:
    """结构化数据提取器"""
    
    def __init__(self):
        self.client = OpenAI(
            api_key=config.OPENAI_API_KEY,
            base_url=config.OPENAI_BASE_URL
        )
        self.model = config.OPENAI_MODEL
    
    def get_extraction_prompt(self) -> str:
        """获取结构化提取的提示词"""
        return """
你是一个专业的金融分析师，需要从研报中提取结构化信息。请仔细分析以下研报内容，提取以下信息：

1. 投资线索 (investment_clues)：
   - 盈利预期变化
   - 产品市场份额
   - 技术创新突破
   - 政策利好
   - 行业趋势
   - 客户拓展
   - 产能扩张等

2. 投资逻辑 (investment_logic)：
   - 核心投资逻辑
   - 支撑证据
   - 影响评估

3. 关键观点 (key_viewpoints)：
   - 研报的核心观点和结论

4. 财务预测 (financial_forecasts)：
   - 营收预测
   - 净利润预测
   - 毛利率预测
   - 成长速率等

5. 估值信息 (valuation_info)：
   - 估值方法
   - 估值结果
   - 目标价格
   - 投资评级

6. 成长驱动因素 (growth_drivers)：
   - 推动公司成长的关键因素

7. 风险因素 (risk_factors)：
   - 可能的风险和挑战

请以JSON格式返回结果，确保数据准确性和完整性。如果某些信息在研报中不存在，请返回空列表或null。

研报内容：
{content}

请返回JSON格式的结构化数据：
"""
    
    def extract_with_llm(self, content: str) -> Optional[Dict]:
        """使用大模型提取结构化数据"""
        try:
            prompt = self.get_extraction_prompt().format(content=content[:8000])  # 限制长度
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的金融分析师，擅长从研报中提取结构化信息。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )
            
            result = response.choices[0].message.content
            
            # 尝试解析JSON
            try:
                # 提取JSON部分
                json_match = re.search(r'\{.*\}', result, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    return json.loads(json_str)
                else:
                    logger.warning("未找到JSON格式的响应")
                    return None
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
                logger.debug(f"原始响应: {result}")
                return None
                
        except Exception as e:
            logger.error(f"大模型提取失败: {e}")
            return None
    
    def validate_and_clean_data(self, raw_data: Dict) -> Optional[StructuredData]:
        """验证和清理提取的数据"""
        try:
            # 提取投资线索
            investment_clues = []
            if 'investment_clues' in raw_data:
                for clue in raw_data['investment_clues']:
                    if isinstance(clue, dict) and 'type' in clue and 'content' in clue:
                        investment_clues.append(InvestmentClue(
                            type=clue.get('type', ''),
                            content=clue.get('content', ''),
                            confidence=clue.get('confidence', 0.8)
                        ))
            
            # 提取投资逻辑
            investment_logic = []
            if 'investment_logic' in raw_data:
                for logic in raw_data['investment_logic']:
                    if isinstance(logic, dict):
                        investment_logic.append(InvestmentLogic(
                            logic=logic.get('logic', ''),
                            supporting_evidence=logic.get('supporting_evidence', []),
                            impact_assessment=logic.get('impact_assessment', '正面')
                        ))
            
            # 提取财务预测
            financial_forecasts = []
            if 'financial_forecasts' in raw_data:
                for forecast in raw_data['financial_forecasts']:
                    if isinstance(forecast, dict):
                        financial_forecasts.append(FinancialForecast(
                            metric=forecast.get('metric', ''),
                            period=forecast.get('period', ''),
                            value=forecast.get('value'),
                            unit=forecast.get('unit', ''),
                            change_rate=forecast.get('change_rate')
                        ))
            
            # 提取估值信息
            valuation_info = []
            if 'valuation_info' in raw_data:
                for valuation in raw_data['valuation_info']:
                    if isinstance(valuation, dict):
                        valuation_info.append(ValuationInfo(
                            method=valuation.get('method', ''),
                            value=valuation.get('value'),
                            target_price=valuation.get('target_price'),
                            rating=valuation.get('rating', '')
                        ))
            
            # 计算数据质量评分
            quality_score = self.calculate_quality_score(
                investment_clues, investment_logic, 
                raw_data.get('key_viewpoints', []),
                financial_forecasts, valuation_info
            )
            
            return StructuredData(
                investment_clues=investment_clues,
                investment_logic=investment_logic,
                key_viewpoints=raw_data.get('key_viewpoints', []),
                financial_forecasts=financial_forecasts,
                valuation_info=valuation_info,
                growth_drivers=raw_data.get('growth_drivers', []),
                risk_factors=raw_data.get('risk_factors', []),
                quality_score=quality_score
            )
            
        except Exception as e:
            logger.error(f"数据验证和清理失败: {e}")
            return None
    
    def calculate_quality_score(self, investment_clues, investment_logic, 
                              key_viewpoints, financial_forecasts, valuation_info) -> float:
        """计算数据质量评分"""
        score = 0.0
        
        # 投资线索权重 0.3
        if investment_clues:
            score += 0.3 * min(len(investment_clues) / 3, 1.0)
        
        # 投资逻辑权重 0.3
        if investment_logic:
            score += 0.3 * min(len(investment_logic) / 2, 1.0)
        
        # 关键观点权重 0.2
        if key_viewpoints:
            score += 0.2 * min(len(key_viewpoints) / 3, 1.0)
        
        # 财务预测权重 0.1
        if financial_forecasts:
            score += 0.1 * min(len(financial_forecasts) / 2, 1.0)
        
        # 估值信息权重 0.1
        if valuation_info:
            score += 0.1 * min(len(valuation_info) / 1, 1.0)
        
        return round(score, 2)
    
    def extract_structured_data(self, content: str) -> Optional[StructuredData]:
        """提取结构化数据的主函数"""
        logger.info("开始提取结构化数据")
        
        # 使用大模型提取
        raw_data = self.extract_with_llm(content)
        if not raw_data:
            logger.warning("大模型提取失败")
            return None
        
        # 验证和清理数据
        structured_data = self.validate_and_clean_data(raw_data)
        if not structured_data:
            logger.warning("数据验证失败")
            return None
        
        logger.info(f"结构化数据提取完成，质量评分: {structured_data.quality_score}")
        return structured_data
    
    def to_dict(self, structured_data: StructuredData) -> Dict:
        """将结构化数据转换为字典"""
        return asdict(structured_data)
