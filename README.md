# 研报RAG分析系统

一个基于大模型的研报结构化数据提取和智能问答系统，能够从PDF研报中提取投资线索、投资逻辑、观点等结构化信息，并提供基于RAG的智能问答功能。

## 🚀 功能特性

### 核心功能
- **PDF研报解析**: 支持批量解析PDF研报，提取文本和表格信息
- **结构化数据提取**: 使用大模型提取投资线索、投资逻辑、财务预测等结构化信息
- **向量数据库**: 基于ChromaDB构建向量数据库，支持语义检索
- **RAG问答系统**: 结合检索增强生成技术，提供智能问答服务
- **Web界面**: 用户友好的Streamlit界面，支持文件上传、数据分析、智能问答

### 提取的结构化信息
1. **投资线索**: 盈利预期、产品份额、技术创新、政策利好等
2. **投资逻辑**: 核心投资逻辑、支撑证据、影响评估
3. **关键观点**: 研报的核心观点和结论
4. **财务预测**: 营收、净利润、毛利率、成长速率等预测数据
5. **估值信息**: 估值方法、目标价格、投资评级
6. **成长驱动因素**: 推动公司成长的关键因素
7. **风险因素**: 可能的风险和挑战

## 📁 项目结构

```
研报RAG系统/
├── data/                          # 研报PDF文件目录
├── output/                        # 输出目录
│   ├── structured_data/          # 结构化数据JSON文件
│   └── reports/                  # 处理报告
├── vector_db/                    # 向量数据库存储
├── logs/                         # 日志文件
├── config.py                     # 系统配置
├── pdf_parser.py                 # PDF解析模块
├── structured_extractor.py       # 结构化数据提取模块
├── vector_store.py               # 向量数据库模块
├── rag_system.py                 # RAG问答系统
├── main_processor.py             # 主处理器
├── streamlit_app.py              # Web界面
├── run_system.py                 # 系统启动脚本
├── requirements.txt              # 依赖包列表
└── README.md                     # 项目说明
```

## 🛠️ 安装和配置

### 1. 环境要求
- Python 3.8+
- 8GB+ RAM (推荐)
- 2GB+ 磁盘空间

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置API密钥
复制 `.env.example` 为 `.env` 并配置：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4

# 或者使用其他兼容OpenAI API的服务
# OPENAI_BASE_URL=https://api.deepseek.com/v1
# OPENAI_MODEL=deepseek-chat
```

### 4. 准备数据
将PDF研报文件放入 `data/` 目录中。

## 🚀 快速开始

### 1. 检查系统状态
```bash
python run_system.py --check
```

### 2. 处理PDF文件
```bash
python run_system.py --process
```

### 3. 启动Web界面
```bash
python run_system.py --web
```

### 4. 交互式问答
```bash
python run_system.py --chat
```

### 5. 测试系统
```bash
python run_system.py --test
```

### 6. 导出数据
```bash
python run_system.py --export
```

## 💻 Web界面使用

启动Web界面后，访问 http://localhost:8501

### 主要功能页面：

1. **系统概览**: 查看系统状态、数据统计、质量分布
2. **文档管理**: 上传PDF文件、批量处理、数据导出
3. **智能问答**: 与系统进行对话，获取研报相关信息
4. **数据分析**: 质量评分分析、机构分析、时间趋势等
5. **系统设置**: API配置、系统维护

## 🔧 系统架构

### 数据处理流程
```mermaid
graph TD
    A[PDF研报] --> B[PDF解析器]
    B --> C[文本提取]
    C --> D[结构化数据提取]
    D --> E[向量化存储]
    E --> F[RAG问答系统]
    
    G[用户问题] --> H[向量检索]
    H --> I[相关文档召回]
    I --> J[大模型生成答案]
    J --> K[返回结果]
```

### 核心模块

1. **PDF解析器** (`pdf_parser.py`)
   - 支持PyMuPDF和pdfplumber双引擎
   - 提取文本、表格和元数据
   - 智能文件名解析

2. **结构化提取器** (`structured_extractor.py`)
   - 基于大模型的信息提取
   - 多维度结构化数据
   - 质量评分机制

3. **向量数据库** (`vector_store.py`)
   - ChromaDB持久化存储
   - 多语言嵌入模型
   - 智能文本分块

4. **RAG系统** (`rag_system.py`)
   - 检索增强生成
   - 置信度评估
   - 来源追踪

## 📊 提示词设计

### 结构化提取提示词
系统使用精心设计的提示词来提取结构化信息：

```
你是一个专业的金融分析师，需要从研报中提取结构化信息。请仔细分析以下研报内容，提取以下信息：

1. 投资线索 (investment_clues)：
   - 盈利预期变化
   - 产品市场份额
   - 技术创新突破
   - 政策利好
   - 行业趋势
   - 客户拓展
   - 产能扩张等

2. 投资逻辑 (investment_logic)：
   - 核心投资逻辑
   - 支撑证据
   - 影响评估

...
```

### RAG问答提示词
```
你是一个专业的金融分析师和投资顾问，专门分析股票研报并回答投资相关问题。

你的任务是：
1. 基于提供的研报内容，准确回答用户的问题
2. 提供客观、专业的分析和建议
3. 引用具体的数据和事实支撑你的观点
4. 如果信息不足，明确说明并建议需要更多信息
5. 保持中性立场，不做过度乐观或悲观的判断
```

## 📈 数据质量评估

系统采用多维度质量评分机制：

- **投资线索权重**: 30%
- **投资逻辑权重**: 30%
- **关键观点权重**: 20%
- **财务预测权重**: 10%
- **估值信息权重**: 10%

质量等级：
- **高质量** (0.8-1.0): 信息完整，逻辑清晰
- **中等质量** (0.5-0.8): 信息较完整，部分缺失
- **低质量** (0.0-0.5): 信息不足，需要人工审核

## 🔍 使用示例

### 命令行使用
```bash
# 检查系统状态
python run_system.py --check

# 处理所有PDF文件
python run_system.py --process

# 启动交互式问答
python run_system.py --chat
> 请输入您的问题: 太辰光的主要业务是什么？
> 答案: 太辰光是一家专业从事光通信器件研发、生产和销售的高新技术企业...
```

### Python API使用
```python
from main_processor import MainProcessor
from rag_system import RAGSystem

# 初始化系统
processor = MainProcessor()
rag_system = RAGSystem()

# 处理PDF文件
results = processor.process_all_pdfs()

# 问答
response = rag_system.answer_question("太辰光的投资价值如何？")
print(f"答案: {response.answer}")
print(f"置信度: {response.confidence}")
```

## 🚨 注意事项

1. **API配置**: 确保正确配置OpenAI API密钥
2. **内存使用**: 处理大量PDF文件时注意内存使用
3. **数据质量**: 低质量评分的数据建议人工审核
4. **模型选择**: 推荐使用GPT-4获得更好的提取效果
5. **网络连接**: 确保网络连接稳定，避免API调用失败

## 🔧 故障排除

### 常见问题

1. **PDF解析失败**
   - 检查PDF文件是否损坏
   - 尝试不同的解析引擎

2. **结构化提取质量低**
   - 检查API配置是否正确
   - 考虑使用更强的模型

3. **向量检索无结果**
   - 确认文档已正确处理
   - 检查查询关键词

4. **Web界面无法访问**
   - 检查端口是否被占用
   - 确认防火墙设置

## 📝 开发计划

- [ ] 支持更多文档格式 (Word, Excel)
- [ ] 增加多语言支持
- [ ] 优化大文档处理性能
- [ ] 添加用户权限管理
- [ ] 集成更多向量数据库
- [ ] 支持实时数据更新

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者
